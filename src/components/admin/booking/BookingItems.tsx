import React, { useState, useEffect } from 'react';
import { Plus, Minus, Clock, Check, X, DollarSign, Trash2, Search, ChevronLeft, ChevronRight, Camera, Edit } from 'lucide-react';
import { Product, Booking, SystemSettings } from '../../../types';
import { BookingProduct } from '../../../types/bookingTypes';
import { formatCurrency } from '../../../utils';
import { getProducts, updateBooking } from '../../../services/database';
import { calculateItemTotal, calculateTotalAmount } from '../../../utils/calculations';

interface BookingItemsProps {
  booking: Booking;
  products?: Product[];
  systemSettings?: SystemSettings;
  formatCurrency?: (amount: number) => string;
  onBookingUpdate?: (booking: Booking) => void;
  onUpdate?: (booking: Booking) => void;
  maxItemsPerPage?: number;
}

const BookingItems: React.FC<BookingItemsProps> = ({
  booking,
  products: providedProducts,
  systemSettings,
  formatCurrency: providedFormatCurrency,
  onBookingUpdate,
  onUpdate,
  maxItemsPerPage = 10
}) => {
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [customDays, setCustomDays] = useState<number>(0);
  const [editingPrice, setEditingPrice] = useState<string | null>(null);
  const [temporaryPrice, setTemporaryPrice] = useState('');
  const [showAddItem, setShowAddItem] = useState(false);
  const [showAddTempItem, setShowAddTempItem] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [tempItem, setTempItem] = useState({
    name: '',
    description: '',
    dailyRate: '',
    quantity: 1
  });
  const itemsPerPage = maxItemsPerPage;

  // For pagination of booking items (not the product selector)
  const [currentBookingItemsPage, setCurrentBookingItemsPage] = useState(1);

  // Handle combined callback functions
  const updateBooking = (updatedBooking: Booking) => {
    if (onBookingUpdate) onBookingUpdate(updatedBooking);
    if (onUpdate) onUpdate(updatedBooking);
  };

  // Use provided formatCurrency or the imported one
  const formatCurrencyFn = providedFormatCurrency || formatCurrency;

  // Fetch all products when component mounts if not provided
  useEffect(() => {
    if (providedProducts) {
      setAllProducts(providedProducts);
    } else {
      const fetchProducts = async () => {
        try {
          const products = await getProducts();
          setAllProducts(products);
        } catch (error) {
          console.error('Error fetching products:', error);
        }
      };
      fetchProducts();
    }
  }, [providedProducts]);

  // Reset page when search query changes
  useEffect(() => {
    setCurrentBookingItemsPage(1);
  }, [searchQuery]);

  const handleEditItem = (productId: string) => {
    const product = booking.products.find(p => p.id === productId);
    if (product) {
      setCustomDays(product.customDays || booking.rentalPeriod.days);
      setEditingItemId(productId);
    }
  };

  const handleQuantityChange = (productId: string, change: number) => {
    const updatedProducts = booking.products.map(product => {
      if (product.id === productId) {
        const newQuantity = Math.max(1, Math.min(product.stock, (product.quantity || 1) + change));
        return { ...product, quantity: newQuantity };
      }
      return product;
    });

    const updatedBooking = updateBookingFinancials(updatedProducts);
    updateBooking(updatedBooking);
  };

  const handleRemoveItem = (productId: string) => {
    const updatedProducts = booking.products.filter(product => product.id !== productId);
    const updatedBooking = updateBookingFinancials(updatedProducts);
    updateBooking(updatedBooking);
  };

  const handleCustomDaysChange = (productId: string, days: number) => {
      const updatedProducts = booking.products.map(product => {
      if (product.id === productId) {
        return { ...product, customDays: days };
        }
        return product;
      });

    const updatedBooking = updateBookingFinancials(updatedProducts);
      updateBooking(updatedBooking);
  };

  const handleTemporaryPriceChange = (productId: string, price: string) => {
    const numericPrice = parseFloat(price);
    if (!isNaN(numericPrice) && numericPrice >= 0) {
      const updatedProducts = booking.products.map(product => {
        if (product.id === productId) {
          return {
            ...product,
            temporaryDailyRate: numericPrice,
            temporaryWeeklyRate: numericPrice * 6 // Set weekly rate as 6x daily rate
          };
        }
        return product;
      });

      const updatedBooking = updateBookingFinancials(updatedProducts);
      updateBooking(updatedBooking);
    }
  };

  const handleClearTemporaryPrice = (productId: string) => {
    const updatedProducts = booking.products.map(product => {
      if (product.id === productId) {
        const { temporaryDailyRate, temporaryWeeklyRate, ...rest } = product;
        return rest;
      }
      return product;
    });

    const updatedBooking = updateBookingFinancials(updatedProducts);
    updateBooking(updatedBooking);
  };

  const handleAddItem = (product: Product) => {
    const existingProduct = booking.products.find(p => p.id === product.id);
    if (existingProduct) {
      handleQuantityChange(product.id, 1);
    } else {
      const updatedProducts = [...booking.products, { ...product, quantity: 1 }];
      const updatedBooking = updateBookingFinancials(updatedProducts);
      updateBooking(updatedBooking);
    }

    setShowAddItem(false);
  };

  const handleAddTempItem = () => {
    if (!tempItem.name || !tempItem.dailyRate) return;

    const newTempProduct: Product = {
      id: Date.now().toString(),
      name: tempItem.name,
      category: 'temporary',
      dailyRate: parseFloat(tempItem.dailyRate),
      description: tempItem.description,
      image: 'https://placehold.co/400x300/2563eb/ffffff?text=Camera',
      available: true,
      quantity: tempItem.quantity,
      stock: 99,
      featured: false,
      sku: `TEMP-${Date.now()}`,
      barcode: `TEMP-${Date.now()}`
    };

    handleAddItem(newTempProduct);
    setShowAddTempItem(false);
    setTempItem({
      name: '',
      description: '',
      dailyRate: '',
      quantity: 1
    });
  };

  // Filter and paginate products for the Add Item modal
  const filteredProducts = allProducts
    .filter(product => product.available)
    .filter(product =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.description.toLowerCase().includes(searchQuery.toLowerCase())
    );

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Filter and paginate booking items
  const filteredBookingItems = booking.products
    .filter(product =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.category.toLowerCase().includes(searchQuery.toLowerCase())
    );

  const totalBookingItemsPages = Math.ceil(filteredBookingItems.length / itemsPerPage);
  const paginatedBookingItems = filteredBookingItems.slice(
    (currentBookingItemsPage - 1) * itemsPerPage,
    currentBookingItemsPage * itemsPerPage
  );

  // Handle page changes
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleBookingItemsPageChange = (page: number) => {
    setCurrentBookingItemsPage(page);
  };

  const calculateItemTotal = (product: Product | BookingProduct) => {
    const productDays = product.customDays || booking.rentalPeriod.days;
    const dailyRate = product.temporaryDailyRate || product.dailyRate;
    const weeklyRate = product.temporaryWeeklyRate || product.weeklyRate;

    if (booking.rentalPeriod.rentalType === 'weekly' && weeklyRate) {
      const weeks = Math.ceil(productDays / 7);
      return weeklyRate * weeks * (product.quantity || 1);
    } else {
      return dailyRate * productDays * (product.quantity || 1);
    }
  };

  const calculateTotalAmount = (products: (Product | BookingProduct)[]) => {
    // Calculate subtotal
    const subtotal = products.reduce((sum, product) => sum + calculateItemTotal(product), 0);

    // Add delivery fee
    const deliveryFee = booking.delivery?.fee || 0;
    const subtotalWithDelivery = subtotal + deliveryFee;

    // Calculate discount
    const discount = booking.coupon ?
      (booking.coupon.discountType === 'percentage' ?
        (subtotalWithDelivery * booking.coupon.discountValue / 100) :
        booking.coupon.discountValue) : 0;

    // Calculate tax
    const taxableAmount = subtotalWithDelivery - discount;
    const tax = taxableAmount * (systemSettings?.enableTax ? systemSettings?.taxRate / 100 : 0);

    // Return final total
    return booking.status === 'cancelled' ? 0 : (taxableAmount + tax);
  };

  // Helper function to update all booking financial values
  const updateBookingFinancials = (updatedProducts: (Product | BookingProduct)[]) => {
    // Use provided systemSettings or default to some safe values
    const taxRate = systemSettings?.enableTax ? systemSettings?.taxRate : 0;
    const enableTax = systemSettings?.enableTax || false;

    // Calculate subtotal
    const subtotal = updatedProducts.reduce((sum, product) => sum + calculateItemTotal(product), 0);

    // Add delivery fee
    const deliveryFee = booking.delivery?.fee || 0;
    const subtotalWithDelivery = subtotal + deliveryFee;

    // Calculate discount
    const discount = booking.coupon ?
      (booking.coupon.discountType === 'percentage' ?
        (subtotalWithDelivery * booking.coupon.discountValue / 100) :
        booking.coupon.discountValue) : 0;

    // Calculate tax
    const taxableAmount = subtotalWithDelivery - discount;
    const tax = taxableAmount * (enableTax ? taxRate / 100 : 0);

    // Calculate final total
    const totalAmount = booking.status === 'cancelled' ? 0 : (taxableAmount + tax);

    return {
      ...booking,
      products: updatedProducts,
      subtotal: subtotal,
      deliveryFee: deliveryFee,
      discount: discount,
      tax: tax,
      totalAmount: totalAmount,
      total: totalAmount
    };
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
      <div className="flex justify-between items-center mb-3">
        <h4 className="font-medium text-gray-900">Booking Items</h4>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowAddTempItem(true)}
            className="px-3 py-1 bg-green-600 text-white rounded-md text-sm flex items-center"
          >
            <Plus size={14} className="mr-1" />
            Quick Add
          </button>
          <button
            onClick={() => setShowAddItem(true)}
            className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm flex items-center"
          >
            <Plus size={14} className="mr-1" />
            Add Item
          </button>
        </div>
      </div>

      {/* Search bar for items in booking */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search in current items..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
      </div>

      {/* Items Table - Desktop */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Item</th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Rate</th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Quantity</th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Days</th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Total</th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {paginatedBookingItems.map(product => (
                <tr key={product.id}>
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex-shrink-0 rounded overflow-hidden mr-3">
                        <img src={product.image} alt={product.name} className="h-full w-full object-cover" />
                      </div>
                      <div>
                        <p className="font-medium">{product.name}</p>
                        <p className="text-xs text-gray-500 capitalize">{product.category}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-right">
                    {editingPrice === product.id ? (
                      <div className="flex items-center justify-end">
                        <div className="relative">
                          <DollarSign size={16} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                          <input
                            type="number"
                            value={temporaryPrice}
                            onChange={(e) => setTemporaryPrice(e.target.value)}
                            className="pl-8 pr-2 py-1 w-24 border border-gray-300 rounded-md text-sm"
                            placeholder="0.00"
                            min="0"
                            step="0.01"
                          />
                        </div>
                        <div className="flex ml-2">
                          <button
                            onClick={() => {
                              handleTemporaryPriceChange(product.id, temporaryPrice);
                              setEditingPrice(null);
                              setTemporaryPrice('');
                            }}
                            className="p-1 rounded-full text-green-600 hover:bg-green-100"
                          >
                            <Check size={16} />
                          </button>
                          <button
                            onClick={() => {
                              setEditingPrice(null);
                              setTemporaryPrice('');
                            }}
                            className="p-1 rounded-full text-red-600 hover:bg-red-100"
                          >
                            <X size={16} />
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-end">
                        {product.temporaryDailyRate ? (
                          <div className="text-right">
                            <div className="line-through text-gray-400">
                              {formatCurrencyFn(product.dailyRate)}/day
                            </div>
                            <div className="text-green-600">
                              {formatCurrencyFn(product.temporaryDailyRate)}/day
                            </div>
                            <div className="mt-1">
                              <button
                                onClick={() => {
                                  setEditingPrice(product.id);
                                  setTemporaryPrice(product.temporaryDailyRate?.toString() || '');
                                }}
                                className="text-xs bg-blue-50 px-2 py-1 rounded text-blue-600 hover:bg-blue-100 flex items-center"
                              >
                                <Edit size={14} className="mr-1" /> Edit Price
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="text-right">
                            <div>{formatCurrencyFn(product.dailyRate)}/day</div>
                            <div className="mt-1">
                              <button
                                onClick={() => {
                                  setEditingPrice(product.id);
                                  setTemporaryPrice(product.temporaryDailyRate?.toString() || '');
                                }}
                                className="text-xs bg-blue-50 px-2 py-1 rounded text-blue-600 hover:bg-blue-100 flex items-center"
                              >
                                <Edit size={14} className="mr-1" /> Edit Price
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center justify-center">
                      <button
                        onClick={() => handleQuantityChange(product.id, -1)}
                        className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                        disabled={product.quantity <= 1}
                      >
                        <Minus size={16} />
                      </button>
                      <span className="mx-3 font-medium">{product.quantity}</span>
                      <button
                        onClick={() => handleQuantityChange(product.id, 1)}
                        className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                        disabled={product.quantity >= product.stock}
                      >
                        <Plus size={16} />
                      </button>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-center">
                    {editingItemId === product.id ? (
                      <div className="flex items-center justify-center space-x-2">
                        <input
                          type="number"
                          value={customDays}
                          onChange={(e) => setCustomDays(Math.max(1, parseInt(e.target.value) || 1))}
                          className="w-16 p-1 border border-gray-300 rounded text-center"
                          min="1"
                        />
                        <div className="flex">
                          <button
                            onClick={() => handleCustomDaysChange(product.id, customDays)}
                            className="p-1 rounded-full text-green-600 hover:bg-green-100"
                            title="Save"
                          >
                            <Check size={16} />
                          </button>
                          <button
                            onClick={() => setEditingItemId(null)}
                            className="p-1 rounded-full text-red-600 hover:bg-red-100"
                            title="Cancel"
                          >
                            <X size={16} />
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <span className="font-medium">
                          {product.customDays || booking.rentalPeriod.days} {(product.customDays || booking.rentalPeriod.days) === 1 ? 'day' : 'days'}
                        </span>
                        <button
                          onClick={() => handleEditItem(product.id)}
                          className="ml-2 p-1 rounded-full text-blue-600 hover:bg-blue-100"
                          title="Edit Days"
                        >
                          <Clock size={16} />
                        </button>
                      </div>
                    )}
                  </td>
                  <td className="px-4 py-3 text-right font-medium">
                    {formatCurrencyFn(calculateItemTotal(product))}
                  </td>
                  <td className="px-4 py-3 text-center">
                    <button
                      onClick={() => handleRemoveItem(product.id)}
                      className="p-1 rounded-full text-red-600 hover:bg-red-100"
                      title="Remove"
                    >
                      <Trash2 size={18} />
                    </button>
                  </td>
                </tr>
              ))}
          </tbody>
          <tfoot className="bg-gray-50">
            <tr>
              <td colSpan={4} className="px-4 py-3 text-right font-medium">Total:</td>
              <td className="px-4 py-3 text-right font-bold">
                {formatCurrencyFn(calculateTotalAmount(booking.products))}
              </td>
              <td></td>
            </tr>
          </tfoot>
        </table>

        {/* Pagination for booking items in desktop view */}
        {totalBookingItemsPages > 1 && (
          <div className="flex justify-center items-center mt-4 space-x-2">
            <button
              onClick={() => handleBookingItemsPageChange(currentBookingItemsPage - 1)}
              disabled={currentBookingItemsPage === 1}
              className={`p-2 rounded-md ${
                currentBookingItemsPage === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
              }`}
            >
              <ChevronLeft size={16} />
            </button>

            <span className="text-sm text-gray-600">
              Page {currentBookingItemsPage} of {totalBookingItemsPages}
            </span>

            <button
              onClick={() => handleBookingItemsPageChange(currentBookingItemsPage + 1)}
              disabled={currentBookingItemsPage === totalBookingItemsPages}
              className={`p-2 rounded-md ${
                currentBookingItemsPage === totalBookingItemsPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
              }`}
            >
              <ChevronRight size={16} />
            </button>
          </div>
        )}
      </div>

      {/* Mobile View for Items */}
      <div className="md:hidden">
        {paginatedBookingItems.map(product => (
            <div key={product.id} className="border rounded-md mb-3 overflow-hidden">
              <div className="bg-gray-50 p-3 flex justify-between items-center">
                <div className="font-medium">
                  <span className="text-gray-600">{product.quantity}x</span> {product.name}
                </div>
                <button
                  onClick={() => handleRemoveItem(product.id)}
                  className="p-1 rounded-full text-red-600 hover:bg-red-100"
                  title="Remove"
                >
                  <Trash2 size={16} />
                </button>
              </div>

              <div className="p-3">
                {/* Days */}
                <div className="flex justify-between items-center mb-2 border-b pb-2">
                  <span className="text-sm text-gray-600">Days:</span>
                  {editingItemId === product.id ? (
                    <div className="flex items-center space-x-2">
                      <input
                        type="number"
                        value={customDays}
                        onChange={(e) => setCustomDays(Math.max(1, parseInt(e.target.value) || 1))}
                        className="w-16 p-1 border border-gray-300 rounded text-center"
                        min="1"
                      />
                      <div className="flex">
                        <button
                          onClick={() => handleCustomDaysChange(product.id, customDays)}
                          className="p-1 rounded-full text-green-600 hover:bg-green-100"
                        >
                          <Check size={16} />
                        </button>
                        <button
                          onClick={() => setEditingItemId(null)}
                          className="p-1 rounded-full text-red-600 hover:bg-red-100"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <span className="font-medium mr-2">
                        {product.customDays || booking.rentalPeriod.days} {(product.customDays || booking.rentalPeriod.days) === 1 ? 'day' : 'days'}
                      </span>
                      <button
                        onClick={() => handleEditItem(product.id)}
                        className="p-1 rounded-full text-blue-600 hover:bg-blue-100"
                      >
                        <Clock size={16} />
                      </button>
                    </div>
                  )}
                </div>

                {/* Quantity */}
                <div className="flex justify-between items-center mb-2 border-b pb-2">
                  <span className="text-sm text-gray-600">Quantity:</span>
                  <div className="flex items-center">
                    <button
                      onClick={() => handleQuantityChange(product.id, -1)}
                      className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                      disabled={product.quantity <= 1}
                    >
                      <Minus size={14} />
                    </button>
                    <span className="mx-3 font-medium">{product.quantity}</span>
                    <button
                      onClick={() => handleQuantityChange(product.id, 1)}
                      className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                      disabled={product.quantity >= product.stock}
                    >
                      <Plus size={14} />
                    </button>
                  </div>
                </div>

                {/* Rate */}
                <div className="flex justify-between items-center mb-2 border-b pb-2">
                  <span className="text-sm text-gray-600">Rate:</span>
                  {editingPrice === product.id ? (
                    <div className="flex items-center">
                      <div className="relative">
                        <DollarSign size={14} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="number"
                          value={temporaryPrice}
                          onChange={(e) => setTemporaryPrice(e.target.value)}
                          className="pl-8 pr-2 py-1 w-24 border border-gray-300 rounded-md text-sm"
                          placeholder="0.00"
                          min="0"
                          step="0.01"
                        />
                      </div>
                      <div className="flex ml-2">
                        <button
                          onClick={() => {
                            handleTemporaryPriceChange(product.id, temporaryPrice);
                            setEditingPrice(null);
                            setTemporaryPrice('');
                          }}
                          className="p-1 rounded-full text-green-600 hover:bg-green-100"
                        >
                          <Check size={14} />
                        </button>
                        <button
                          onClick={() => {
                            setEditingPrice(null);
                            setTemporaryPrice('');
                          }}
                          className="p-1 rounded-full text-red-600 hover:bg-red-100"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-end">
                      {product.temporaryDailyRate ? (
                        <div>
                          <div>
                            <span className="line-through text-gray-400 mr-2">
                              {formatCurrencyFn(product.dailyRate)}/day
                            </span>
                            <span className="text-green-600">
                              {formatCurrencyFn(product.temporaryDailyRate)}/day
                            </span>
                          </div>
                          <div className="mt-1 flex justify-end">
                            <button
                              onClick={() => {
                                setEditingPrice(product.id);
                                setTemporaryPrice(product.temporaryDailyRate?.toString() || '');
                              }}
                              className="text-xs bg-blue-50 px-2 py-1 rounded text-blue-600 hover:bg-blue-100 flex items-center"
                            >
                              <Edit size={14} className="mr-1" /> Edit Price
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div>{formatCurrencyFn(product.dailyRate)}/day</div>
                          <div className="mt-1">
                            <button
                              onClick={() => {
                                setEditingPrice(product.id);
                                setTemporaryPrice(product.temporaryDailyRate?.toString() || '');
                              }}
                              className="text-xs bg-blue-50 px-2 py-1 rounded text-blue-600 hover:bg-blue-100 flex items-center"
                            >
                              <Edit size={14} className="mr-1" /> Edit Price
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Total */}
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total:</span>
                  <span className="font-medium">{formatCurrencyFn(calculateItemTotal(product))}</span>
                </div>
              </div>
            </div>
          ))}

        {/* Total for mobile */}
        <div className="bg-gray-50 p-3 rounded-md border flex justify-between items-center mb-4">
          <span className="font-medium">Items Total:</span>
          <span className="font-medium">{formatCurrencyFn(calculateTotalAmount(booking.products))}</span>
        </div>

        {/* Pagination for booking items in mobile view */}
        {totalBookingItemsPages > 1 && (
          <div className="flex justify-center items-center mb-4 space-x-2">
            <button
              onClick={() => handleBookingItemsPageChange(currentBookingItemsPage - 1)}
              disabled={currentBookingItemsPage === 1}
              className={`p-2 rounded-md ${
                currentBookingItemsPage === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
              }`}
            >
              <ChevronLeft size={16} />
            </button>

            <span className="text-sm text-gray-600">
              Page {currentBookingItemsPage} of {totalBookingItemsPages}
            </span>

            <button
              onClick={() => handleBookingItemsPageChange(currentBookingItemsPage + 1)}
              disabled={currentBookingItemsPage === totalBookingItemsPages}
              className={`p-2 rounded-md ${
                currentBookingItemsPage === totalBookingItemsPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
              }`}
            >
              <ChevronRight size={16} />
            </button>
          </div>
        )}
      </div>

      {/* Add Temporary Item Modal */}
      {showAddTempItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold">Add Temporary Item</h2>
              <button
                onClick={() => setShowAddTempItem(false)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              {/* Image Placeholder */}
              <div className="bg-blue-100 rounded-lg p-8 flex items-center justify-center">
                <Camera size={64} className="text-blue-600" />
              </div>

              {/* Name */}
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Item Name*
                </label>
                <input
                  type="text"
                  value={tempItem.name}
                  onChange={(e) => setTempItem({ ...tempItem, name: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="Enter item name"
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Description
                </label>
                <textarea
                  value={tempItem.description}
                  onChange={(e) => setTempItem({ ...tempItem, description: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  rows={3}
                  placeholder="Enter item description"
                />
              </div>

              {/* Daily Rate */}
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Daily Rate (BHD)*
                </label>
                <input
                  type="number"
                  value={tempItem.dailyRate}
                  onChange={(e) => setTempItem({ ...tempItem, dailyRate: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  min="0"
                  step="0.001"
                  placeholder="0.000"
                />
              </div>

              {/* Quantity */}
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Quantity
                </label>
                <input
                  type="number"
                  value={tempItem.quantity}
                  onChange={(e) => setTempItem({ ...tempItem, quantity: parseInt(e.target.value) || 1 })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  min="1"
                />
              </div>
            </div>

            <div className="flex justify-end mt-6 space-x-2">
              <button
                onClick={() => setShowAddTempItem(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleAddTempItem}
                disabled={!tempItem.name || !tempItem.dailyRate}
                className={`px-4 py-2 rounded-md ${
                  tempItem.name && tempItem.dailyRate
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Add Item
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Item Modal */}
      {showAddItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-bold text-lg">Add Item</h3>
              <button
                onClick={() => setShowAddItem(false)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>

            {/* Search Bar */}
            <div className="mb-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search items..."
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md"
                />
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {paginatedProducts.map(product => (
                <div
                  key={product.id}
                  onClick={() => handleAddItem(product)}
                  className="border rounded-md p-3 flex items-center cursor-pointer hover:bg-gray-50"
                >
                  <div className="h-12 w-12 flex-shrink-0 rounded overflow-hidden mr-3">
                    <img src={product.image} alt={product.name} className="h-full w-full object-cover" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{product.name}</p>
                    <p className="text-sm text-gray-500">{formatCurrencyFn(product.dailyRate)}/day</p>
                  </div>
                  <div className="ml-2">
                    <Plus size={16} className="text-blue-600" />
                  </div>
                </div>
              ))}
              {paginatedProducts.length === 0 && (
                <div className="col-span-full text-center py-8 text-gray-500">
                  No items found matching your search.
                </div>
              )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center mt-6 space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`p-2 rounded-md ${
                    currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  <ChevronLeft size={20} />
                </button>

                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let pageToShow;
                  if (totalPages <= 5) {
                    // If 5 or fewer pages, show all page numbers
                    pageToShow = i + 1;
                  } else if (currentPage <= 3) {
                    // We're at the beginning, show first 5 pages
                    pageToShow = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    // We're at the end, show last 5 pages
                    pageToShow = totalPages - 4 + i;
                  } else {
                    // We're in the middle, show current page and 2 on each side
                    pageToShow = currentPage - 2 + i;
                  }
                  return (
                    <button
                      key={pageToShow}
                      onClick={() => handlePageChange(pageToShow)}
                      className={`px-3 py-1 rounded-md ${
                        currentPage === pageToShow
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                      }`}
                    >
                      {pageToShow}
                    </button>
                  );
                })}

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`p-2 rounded-md ${
                    currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  <ChevronRight size={20} />
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingItems;