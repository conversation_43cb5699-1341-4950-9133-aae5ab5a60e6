import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from './ui/Tabs';
import { Product, Booking, Coupon, SystemSettings, User, Vendor } from '../types';
import ProductManagement from './admin/ProductManagement';
import BookingManagement from './admin/BookingManagement';
import CategoryManagement from './admin/CategoryManagement';
import CouponManagement from './admin/CouponManagement';
import SystemSettingsComponent from './admin/SystemSettings';
import ClientManagement from './admin/ClientManagement';
import PaymentManagement from './admin/PaymentManagement';
import VendorManagement from './admin/VendorManagement';
import CalendarView from './admin/CalendarView';
import { Plus, Bell } from 'lucide-react';
import UsersManagement from './UsersManagement';
import { requestNotificationPermission } from '../services/oneSignalService';

interface AdminDashboardProps {
  products: Product[];
  bookings: Booking[];
  coupons: Coupon[];
  categories: string[];
  systemSettings: SystemSettings;
  users: User[];
  currentUser: User;
  vendors: Vendor[];
  onProductsUpdate: (products: Product[]) => void;
  onBookingsUpdate: (bookings: Booking[]) => void;
  onCouponsUpdate: (coupons: Coupon[]) => void;
  onCategoriesUpdate: (categories: string[]) => void;
  onSystemSettingsUpdate: (settings: SystemSettings) => void;
  onUsersUpdate: (users: User[]) => void;
  onVendorUpdate: (vendors: Vendor[]) => void;
  onCreateQuote: () => void;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({
  products,
  bookings,
  coupons,
  categories,
  systemSettings,
  users,
  currentUser,
  vendors,
  onProductsUpdate,
  onBookingsUpdate,
  onCouponsUpdate,
  onCategoriesUpdate,
  onSystemSettingsUpdate,
  onUsersUpdate,
  onVendorUpdate,
  onCreateQuote
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-4 md:p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Admin Dashboard</h1>
        <div className="flex space-x-4">
          <button
            onClick={() => requestNotificationPermission()}
            className="bg-blue-100 text-blue-800 hover:bg-blue-200 p-2 rounded-md flex items-center justify-center"
            title="Enable Notifications"
          >
            <Bell size={18} />
          </button>
          <button
            onClick={onCreateQuote}
            className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          >
            <Plus size={18} className="mr-2" />
            New
          </button>
        </div>
      </div>

      <Tabs defaultValue="bookings">
        <TabsList className="mb-6 overflow-x-auto flex pb-1">
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="calendar">Calendar</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="clients">Clients</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="vendors">Vendors</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="coupons">Coupons</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="bookings">
          <BookingManagement
            bookings={bookings}
            products={products}
            systemSettings={systemSettings}
            onBookingsUpdate={onBookingsUpdate}
          />
        </TabsContent>

        <TabsContent value="calendar">
          <CalendarView
            bookings={bookings}
            products={products}
            systemSettings={systemSettings}
            onBookingSelect={(booking) => {
              // You can implement a function to navigate to the booking details
              console.log('Selected booking:', booking);
            }}
          />
        </TabsContent>

        <TabsContent value="payments">
          <PaymentManagement
            bookings={bookings}
            onBookingsUpdate={onBookingsUpdate}
          />
        </TabsContent>

        <TabsContent value="clients">
          <ClientManagement
            bookings={bookings}
            onBookingsUpdate={onBookingsUpdate}
          />
        </TabsContent>

        <TabsContent value="products">
          <ProductManagement
            products={products}
            categories={categories}
            onProductsUpdate={onProductsUpdate}
          />
        </TabsContent>

        <TabsContent value="vendors">
          <VendorManagement
            onUpdate={onVendorUpdate}
          />
        </TabsContent>

        <TabsContent value="categories">
          <CategoryManagement
            categories={categories}
            products={products}
            onCategoriesUpdate={onCategoriesUpdate}
            onProductsUpdate={onProductsUpdate}
          />
        </TabsContent>

        <TabsContent value="coupons">
          <CouponManagement
            coupons={coupons}
            onCouponsUpdate={onCouponsUpdate}
          />
        </TabsContent>

        <TabsContent value="users">
          <UsersManagement
            initialUsers={users}
            onUserUpdate={onUsersUpdate}
            currentUser={currentUser}
          />
        </TabsContent>

        <TabsContent value="settings">
          <SystemSettingsComponent
            settings={systemSettings}
            onSettingsUpdate={onSystemSettingsUpdate}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminDashboard;