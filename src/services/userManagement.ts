import { User, UserRole } from '../types';

const API_BASE_URL = 'http://localhost:8000/api';

// Helper function for API calls
const apiCall = async (endpoint: string, options: RequestInit = {}): Promise<any> => {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Create a new user (admin only)
export const createUser = async (
  email: string,
  password: string,
  userData: {
    name: string;
    username: string;
    role: UserRole;
  }
): Promise<string> => {
  const result = await apiCall('/users.php', {
    method: 'POST',
    body: JSON.stringify({
      email,
      password,
      ...userData,
    }),
  });
  return result.id;
};

// Update user profile
export const updateUserProfile = async (
  userId: string,
  updates: Partial<User>
): Promise<void> => {
  await apiCall('/users.php', {
    method: 'PUT',
    body: JSON.stringify({
      id: userId,
      ...updates,
    }),
  });
};

// Delete user account
export const deleteUserAccount = async (userId: string): Promise<void> => {
  await apiCall(`/users.php?id=${userId}`, {
    method: 'DELETE',
  });
};

// Get all users (admin only)
export const getUsers = async (): Promise<User[]> => {
  return apiCall('/users.php');
};

// Get user by ID
export const getUserById = async (userId: string): Promise<User | null> => {
  try {
    return await apiCall(`/users.php?id=${userId}`);
  } catch (error) {
    return null;
  }
};

// Update user status (activate/deactivate)
export const updateUserStatus = async (userId: string, isActive: boolean): Promise<void> => {
  await apiCall('/users.php', {
    method: 'PUT',
    body: JSON.stringify({
      id: userId,
      isActive,
    }),
  });
};

// Change user password
export const changeUserPassword = async (userId: string, newPassword: string): Promise<void> => {
  await apiCall('/users.php', {
    method: 'PUT',
    body: JSON.stringify({
      id: userId,
      password: newPassword,
    }),
  });
};
