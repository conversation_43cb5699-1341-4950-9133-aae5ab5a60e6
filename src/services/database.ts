import { Product, Booking, Coupon, Client, ClientActivity, SystemSettings, User, UserRole, Vendor, VendorTransaction, Payment, DeliveryOption } from '../types';
import { sendNewBookingNotification, sendAdminDirectNotification } from './oneSignalService';

const API_BASE_URL = 'http://localhost:8000/api';

// API helper function
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return response.json();
};

// Generate unique ID helper
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Products
export const getProducts = async (): Promise<Product[]> => {
  return apiCall('/equipment.php');
};

export const addProduct = async (product: Omit<Product, 'id'>): Promise<string> => {
  const result = await apiCall('/equipment.php', {
    method: 'POST',
    body: JSON.stringify(product),
  });
  return result.id;
};

export const updateProduct = async (product: Product): Promise<void> => {
  await apiCall('/equipment.php', {
    method: 'PUT',
    body: JSON.stringify(product),
  });
};

export const deleteProduct = async (id: string): Promise<void> => {
  await apiCall(`/equipment.php?id=${id}`, {
    method: 'DELETE',
  });
};

// Categories
export const getCategories = async (): Promise<string[]> => {
  return apiCall('/categories.php');
};

export const addCategory = async (category: { name: string; description?: string }): Promise<string> => {
  const result = await apiCall('/categories.php', {
    method: 'POST',
    body: JSON.stringify(category),
  });
  return result.id;
};

export const deleteCategory = async (categoryName: string): Promise<void> => {
  await apiCall(`/categories.php?name=${encodeURIComponent(categoryName)}`, {
    method: 'DELETE',
  });
};

// Bookings
export const getBookings = async (): Promise<Booking[]> => {
  return apiCall('/bookings.php');
};

export const addBooking = async (booking: Omit<Booking, 'id'>): Promise<string> => {
  const result = await apiCall('/bookings.php', {
    method: 'POST',
    body: JSON.stringify(booking),
  });

  // Send notification (using existing OneSignal implementation)
  try {
    await sendNewBookingNotification({
      customerName: booking.customer.name,
      date: booking.date,
      quoteNumber: booking.quoteNumber
    });
  } catch (error) {
    console.error('Failed to send booking notification:', error);
  }

  return result.id;
};

export const updateBooking = async (booking: Booking): Promise<void> => {
  await apiCall('/bookings.php', {
    method: 'PUT',
    body: JSON.stringify(booking),
  });
};

export const deleteBooking = async (id: string): Promise<void> => {
  await apiCall(`/bookings.php?id=${id}`, {
    method: 'DELETE',
  });
};

// Coupons
export const getCoupons = async (): Promise<Coupon[]> => {
  return apiCall('/coupons.php');
};

export const addCoupon = async (coupon: Omit<Coupon, 'id'>): Promise<string> => {
  const result = await apiCall('/coupons.php', {
    method: 'POST',
    body: JSON.stringify(coupon),
  });
  return result.id;
};

export const updateCoupon = async (coupon: Coupon): Promise<void> => {
  await apiCall('/coupons.php', {
    method: 'PUT',
    body: JSON.stringify(coupon),
  });
};

export const deleteCoupon = async (id: string): Promise<void> => {
  await apiCall(`/coupons.php?id=${id}`, {
    method: 'DELETE',
  });
};

// Clients
export const getClients = async (): Promise<Client[]> => {
  return apiCall('/clients.php');
};

export const addClient = async (client: Omit<Client, 'id'>): Promise<string> => {
  const result = await apiCall('/clients.php', {
    method: 'POST',
    body: JSON.stringify(client),
  });
  return result.id;
};

export const updateClient = async (client: Client): Promise<void> => {
  await apiCall('/clients.php', {
    method: 'PUT',
    body: JSON.stringify(client),
  });
};

export const deleteClient = async (email: string): Promise<void> => {
  await apiCall(`/clients.php?email=${email}`, {
    method: 'DELETE',
  });
};

export const mergeClients = async (primaryClientId: string, secondaryClientId: string): Promise<void> => {
  // TODO: Implement client merge API endpoint
  console.log('mergeClients not yet implemented');
};

// Client Activities
export const getClientActivities = async (): Promise<ClientActivity[]> => {
  // TODO: Implement client activities API endpoint
  return [];
};

export const addClientActivity = async (activity: Omit<ClientActivity, 'id'>): Promise<string> => {
  // TODO: Implement client activities API endpoint
  return generateId();
};

// System Settings
export const getSystemSettings = async (): Promise<SystemSettings> => {
  return apiCall('/settings.php');
};

export const updateSystemSettings = async (settings: SystemSettings): Promise<void> => {
  await apiCall('/settings.php', {
    method: 'PUT',
    body: JSON.stringify(settings),
  });
};

// Users
export const getUsers = async (): Promise<User[]> => {
  return [];
};

export const addUser = async (user: Omit<User, 'id'>): Promise<string> => {
  return generateId();
};

export const authenticateUser = async (username: string, password: string): Promise<User | null> => {
  try {
    const user = await apiCall('/auth/login.php', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
    return user;
  } catch (error) {
    console.error('Authentication failed:', error);
    return null;
  }
};

// Vendors
export const getVendors = async (): Promise<Vendor[]> => {
  // TODO: Implement vendors API endpoint
  return [];
};

export const addVendor = async (vendor: Omit<Vendor, 'id'>): Promise<string> => {
  // TODO: Implement vendors API endpoint
  return generateId();
};

export const updateVendor = async (vendor: Vendor): Promise<void> => {
  // TODO: Implement vendors API endpoint
};

export const deleteVendor = async (id: string): Promise<void> => {
  // TODO: Implement vendors API endpoint
};

export const getVendorRevenueData = async (vendorId: string): Promise<any> => {
  // TODO: Implement vendor revenue API endpoint
  return {};
};

export const updateUserLastLogin = async (userId: string): Promise<void> => {
  // TODO: Implement user last login update API endpoint
  console.log('updateUserLastLogin not yet implemented');
};

// Additional vendor functions
export const getVendorTransactions = async (vendorId: string): Promise<any[]> => {
  // TODO: Implement vendor transactions API endpoint
  return [];
};

export const addVendorTransaction = async (transaction: any): Promise<string> => {
  // TODO: Implement vendor transaction API endpoint
  return generateId();
};

export const getProductsByVendor = async (vendorId: string): Promise<Product[]> => {
  // TODO: Implement vendor products API endpoint
  return [];
};
