export interface Product {
  id: string;
  name: string;
  category: string;
  sku: string;
  barcode?: string;
  dailyRate: number;
  weeklyRate?: number;
  image: string;
  description: string;
  available: boolean;
  quantity: number;
  stock: number;
  featured?: boolean;
  customDays?: number;
  customPrice?: number;
  temporaryDailyRate?: number;
  temporaryWeeklyRate?: number;
  isExternalVendorItem?: boolean;
  vendorId?: string;
  vendorSku?: string;
  vendorCost?: number;
  profitMargin?: number;
  bulkDiscounts?: BulkDiscount[];
}

export interface ContactInfo {
  name: string;
  email: string;
  phone: string;
  address: string;
}

export interface RentalPeriodInfo {
  dates: string[]; // Array of ISO date strings
  days: number;
  rentalType: 'daily' | 'weekly';
}

export interface Coupon {
  id: string;
  code: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  expiryDate: string;
  active: boolean;
}

export interface ItemDiscount {
  productId: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
}

export interface DeliveryOption {
  id: string;
  name: string;
  description: string;
  fee: number;
}

export interface SystemSettings {
  taxRate: number;
  enableTax: boolean;
  deliveryOptions: DeliveryOption[];
  lastQuoteNumber: number;
}

export interface Payment {
  id: string;
  bookingId: string;
  amount: number;
  transactionId: string;
  date: string;
  method: 'cash' | 'card' | 'bank_transfer' | 'cheque' | 'other';
  reference?: string;
  notes?: string;
  status: 'pending' | 'completed' | 'failed';
}

// Import simplified BookingProduct type
import { BookingProduct } from './types/bookingTypes';

export interface Booking {
  id: string;
  quoteNumber: string;
  date: string;
  customer: ContactInfo;
  products: BookingProduct[]; // Using simplified product type for bookings
  rentalPeriod: RentalPeriodInfo;
  coupon?: Coupon | null;
  itemDiscounts?: ItemDiscount[];
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  delivery?: {
    option: DeliveryOption | null;
    fee: number;
  };
  payments?: Payment[];
  totalAmount?: number;
  subtotal: number;
  deliveryFee: number;
  discount: number;
  tax: number;
  total: number;
  paidAmount?: number;
  remainingAmount?: number;
}

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  dateAdded: string;
  totalBookings: number;
  totalSpent: number;
  pendingPayment?: number;
  lastBooking?: string;
  notes?: string;
}

export interface ClientActivity {
  id: string;
  clientId: string;
  date: string;
  type: 'booking' | 'quote' | 'payment' | 'note' | 'merge';
  description: string;
  amount?: number;
  bookingId?: string;
  quoteNumber?: string;
}

export type UserRole = 'admin' | 'manager';

export interface User {
  id: string;
  email: string;
  username: string;
  name: string;
  role: UserRole;
  createdAt: string;
  lastLogin?: string;
  isActive: boolean;
}

export interface MonthlyRevenue {
  month: string;
  revenue: number;
}

export interface ClientStatistics {
  totalClients: number;
  newClientsThisMonth: number;
  activeClients: number;
  topClients: {
    clientId: string;
    name: string;
    totalSpent: number;
  }[];
  monthlyRevenue: MonthlyRevenue[];
}

export interface VendorDiscount {
  type: 'fixed' | 'percentage' | 'total';
  value: number;
  minAmount?: number; // For total amount discounts
  description?: string;
}

export interface Vendor {
  id: string;
  name: string;
  contactName?: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  paymentTerms: string;
  active: boolean;
  createdAt: string;
  taxPercentage?: number;
  discounts?: VendorDiscount[];
}

export interface VendorTransaction {
  id: string;
  vendorId: string;
  amount: number;
  description: string;
  date: string;
  type: 'payment' | 'invoice';
  status: 'pending' | 'completed' | 'cancelled';
  bookingId?: string;
  reference?: string;
}

export interface BulkDiscount {
  minQuantity: number;
  discountPercentage: number;
}