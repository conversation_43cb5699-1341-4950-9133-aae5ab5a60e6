import React, { useState, useEffect } from 'react';
import { Calendar, Phone, Mail, User as UserIcon, Clock, ShoppingCart, FileText, Truck, Shield, X, Plus, Minus, Trash2, Menu } from 'lucide-react';
import ProductList from './components/ProductList';
import ContactForm from './components/ContactForm';
import AddTemporaryProduct from './components/AddTemporaryProduct';
import RentalPeriod from './components/RentalPeriod';
import DeliveryOptions from './components/DeliveryOptions';
import ReviewBooking from './components/ReviewBooking';
import Quote from './components/Quote';
import AdminLogin from './components/AdminLogin';
import AdminDashboard from './components/AdminDashboard';
import Login from './components/Login';
import Logo from './components/Logo';
import { setOneSignalUser } from './services/oneSignalService';
import { Product, Booking, Coupon, DeliveryOption, SystemSettings, Payment, User, UserRole, Vendor } from './types';
import { products as initialProducts } from './data/products';
import { bookings as initialBookings } from './data/bookings';
import { coupons as initialCoupons } from './data/coupons';
import { categories as initialCategories } from './data/categories';
import { systemSettings as initialSystemSettings } from './data/systemSettings';
import { clients as initialClients, clientActivities as initialClientActivities } from './data/clients';
import { Client, ClientActivity } from './types';
import {
  getProducts,
  getBookings,
  getCoupons,
  getClients,
  getClientActivities,
  getSystemSettings,
  addProduct,
  addBooking,
  addClient,
  addClientActivity,
  updateSystemSettings,
  addCoupon,
  updateBooking,
  getCategories,
  updateCoupon,
  updateClient,
  getUsers,
  addUser,
  getVendors,
  authenticateUser
} from './services/database';

import { seedDefaultAdmin } from './services/seedData';
import VendorManagement from './components/admin/VendorManagement';

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // PWA features removed
  const [step, setStep] = useState<number>(1);
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);
  const [contactInfo, setContactInfo] = useState({
    name: '',
    email: '',
    phone: '',
    address: ''
  });
  const [rentalPeriod, setRentalPeriod] = useState({
    startDate: '',
    endDate: '',
    days: 1,
    rentalType: 'daily' as 'daily' | 'weekly'
  });
  const [couponCode, setCouponCode] = useState('');
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);
  const [quoteGenerated, setQuoteGenerated] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [showAdminLogin, setShowAdminLogin] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [currentQuoteNumber, setCurrentQuoteNumber] = useState<string>('');
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [showCreateQuoteModal, setShowCreateQuoteModal] = useState(false);
  const [showAddTempProduct, setShowAddTempProduct] = useState(false);
  const [adminQuoteProducts, setAdminQuoteProducts] = useState<Product[]>([]);
  const [systemSettings, setSystemSettings] = useState<SystemSettings>(initialSystemSettings);
  const [selectedDeliveryOption, setSelectedDeliveryOption] = useState<DeliveryOption | null>(null);
  const [clients, setClients] = useState<Client[]>([]);
  const [clientActivities, setClientActivities] = useState<ClientActivity[]>([]);
  const [adminProducts, setAdminProducts] = useState<Product[]>([]);
  const [payment, setPayment] = useState<Payment>({
    id: '',
    bookingId: '',
    method: 'cash',
    status: 'pending',
    amount: 0,
    transactionId: '',
    date: new Date().toISOString()
  });

  // Updated user states
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Add mobile menu state
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const [vendors, setVendors] = useState<Vendor[]>([]);

  // Define loadData function outside of useEffect
  const loadData = async () => {
    console.log('Loading data from API...');
    setIsLoading(true);
    setError('');

    try {
      const [
        productsData,
        categoriesData,
        bookingsData,
        couponsData,
        systemSettingsData,
        clientsData,
        usersData,
        vendorsData
      ] = await Promise.all([
        getProducts(),
        getCategories(),
        getBookings(),
        getCoupons(),
        getSystemSettings(),
        getClients(),
        getUsers(),
        getVendors()
      ]);

      console.log('Data loaded:');
      console.log('- Products:', productsData.length);
      console.log('- Categories:', categoriesData.length);
      console.log('- Bookings:', bookingsData.length);
      console.log('- Coupons:', couponsData.length);
      console.log('- Clients:', clientsData.length);
      console.log('- Users:', usersData.length);
      console.log('- Vendors:', vendorsData.length);

      // Log users for debugging
      console.log('Users data:', usersData.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        username: user.username || 'NO_USERNAME',
        role: user.role,
        isActive: user.isActive
      })));

      setProducts(productsData);
      setCategories(categoriesData);
      setBookings(bookingsData);
      setCoupons(couponsData);
      setSystemSettings(systemSettingsData);
      setClients(clientsData);
      setUsers(usersData);
      setVendors(vendorsData);

      // If no users, create a default admin
      if (usersData.length === 0) {
        console.log('No users found. Creating default admin...');
        try {
          const admin = await seedDefaultAdmin();
          if (admin) {
            console.log('Default admin created:', admin);
            setUsers([admin]);
          } else {
            console.error('Failed to create default admin');
          }
        } catch (err) {
          console.error('Error creating default admin:', err);
        }
      } else {
        console.log('Users exist, no need to create default admin');
      }

      // Ensure SystemSettings consistency between 'general' and 'settings' documents
      if (systemSettingsData.lastQuoteNumber) {
        console.log('Migrating SystemSettings to ensure consistency');
        await updateSystemSettings(systemSettingsData);
      }

      setIsLoading(false);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Failed to load data. Please refresh the page.');
      setIsLoading(false);
    }
  };

  // Effect for checking stored authentication
  useEffect(() => {
    console.log('Checking stored authentication...');

    // Check if user is stored in localStorage
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        console.log('Found stored user:', user);
        setCurrentUser(user);
        setIsAuthenticated(true);

        // Set user information in OneSignal
        try {
          setOneSignalUser(user);
          console.log('OneSignal user set');
        } catch (notificationError) {
          console.error('Error setting OneSignal user:', notificationError);
        }

        // Load data after authentication is complete
        loadData();
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('currentUser');
        setIsLoading(false);
      }
    } else {
      console.log('No stored user found');
      setIsLoading(false);
    }
  }, []);

  // Add a basic test render
  console.log('App component rendering, isLoading:', isLoading, 'error:', error);

  const handleProductSelect = (products: Product[]) => {
    setSelectedProducts(products);
    window.scrollTo(0, 0);
    setStep(2);
  };

  const handleContactSubmit = (info: typeof contactInfo) => {
    setContactInfo(info);
    window.scrollTo(0, 0);
    setStep(5);
  };

  const handleRentalPeriodSubmit = (period: typeof rentalPeriod) => {
    setRentalPeriod(period);
    window.scrollTo(0, 0);
    setStep(3);
  };

  const handleDeliveryOptionSubmit = (option: DeliveryOption | null) => {
    setSelectedDeliveryOption(option);
    window.scrollTo(0, 0);
    setStep(4);
  };

  const handleReviewSubmit = async () => {
    setQuoteGenerated(true);
    // No longer generating quote number here
    window.scrollTo(0, 0);
    setStep(6);
  };

  const handleQuoteFinalize = async () => {
    try {
      // Generate quote number when finalizing
      if (!currentQuoteNumber) {
        // Get the last quote number, defaulting to 1000 if not set
        const lastNumber = systemSettings.lastQuoteNumber || 1000;
        const nextQuoteNumber = lastNumber + 1;

        // Update system settings with new quote number
        const updatedSettings = {
          ...systemSettings,
          lastQuoteNumber: nextQuoteNumber
        };
        setSystemSettings(updatedSettings);
        await updateSystemSettings(updatedSettings);

        // Format quote number with leading zeros
        const formattedQuoteNumber = `Q-${nextQuoteNumber.toString().padStart(6, '0')}`;
        setCurrentQuoteNumber(formattedQuoteNumber);

        // Wait for the quote number to be set before proceeding
        await new Promise(resolve => setTimeout(resolve, 100));

        // Create booking with the newly generated quote number
        await createBookingWithQuoteNumber(formattedQuoteNumber);
      } else {
        // Use the existing quote number
        await createBookingWithQuoteNumber(currentQuoteNumber);
      }

      // Return true to indicate success
      return true;
    } catch (error) {
      console.error("Error finalizing quote:", error);
      alert(`Error creating booking: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Re-throw the error so it can be caught by the Quote component
      throw error;
    }
  };

  // Helper function to create a booking with a valid quote number
  const createBookingWithQuoteNumber = async (quoteNumber: string) => {
    // Calculate total amount
    const subtotal = selectedProducts.reduce((sum, product) => {
      const rate = rentalPeriod.rentalType === 'daily' ? product.dailyRate : (product.weeklyRate || product.dailyRate);
      return sum + (rate * product.quantity * rentalPeriod.days);
    }, 0);

    const deliveryFee = selectedDeliveryOption?.fee || 0;
    const discount = appliedCoupon ?
      (appliedCoupon.discountType === 'percentage' ?
        subtotal * (appliedCoupon.discountValue / 100) :
        appliedCoupon.discountValue) : 0;

    const taxableAmount = subtotal + deliveryFee - discount;
    const tax = systemSettings.enableTax ? taxableAmount * (systemSettings.taxRate / 100) : 0;
    const totalAmount = taxableAmount + tax;

    // Find or create client
    let clientId = '';

    // Check if client exists by email
    const existingClient = clients.find(c => c.email.toLowerCase() === contactInfo.email.toLowerCase());

    if (existingClient) {
      // Update existing client
      const updatedClient = {
        ...existingClient,
        name: contactInfo.name,
        phone: contactInfo.phone,
        address: contactInfo.address,
        totalBookings: (existingClient.totalBookings || 0) + 1,
        totalSpent: (existingClient.totalSpent || 0) + totalAmount,
        lastBooking: new Date().toISOString()
      };
      // Use updateClient for existing clients
      await updateClient(updatedClient);
      clientId = existingClient.id;
    } else {
      // Create new client
      const newClient: Omit<Client, 'id'> = {
        name: contactInfo.name,
        email: contactInfo.email,
        phone: contactInfo.phone,
        address: contactInfo.address,
        dateAdded: new Date().toISOString(),
        totalBookings: 1,
        totalSpent: totalAmount,
        lastBooking: new Date().toISOString()
      };

      // Try to add the client, but handle the case if it already exists
      try {
        clientId = await addClient(newClient);
      } catch (error) {
        // If client already exists (could happen if they were added between our check and this call)
        if (error instanceof Error && error.message.includes('already exists')) {
          // Use the email as the client ID since that's how our API works
          clientId = contactInfo.email.toLowerCase();

          // Update the existing client
          const existingClients = await getClients();
          const existingClient = existingClients.find(c => c.email.toLowerCase() === contactInfo.email.toLowerCase());

          if (existingClient) {
            const updatedClient = {
              ...existingClient,
              name: contactInfo.name,
              phone: contactInfo.phone,
              address: contactInfo.address,
              totalBookings: (existingClient.totalBookings || 0) + 1,
              totalSpent: (existingClient.totalSpent || 0) + totalAmount,
              lastBooking: new Date().toISOString()
            };

            await updateClient(updatedClient);
          }
        } else {
          throw error; // Re-throw if it's a different error
        }
      }
    }

    // Create a new booking
    const newBooking: Omit<Booking, 'id'> = {
      quoteNumber: quoteNumber,
      date: new Date().toISOString(),
      customer: contactInfo,
      products: selectedProducts,
      rentalPeriod: rentalPeriod,
      coupon: appliedCoupon,
      status: 'pending',
      delivery: selectedDeliveryOption ? {
        option: selectedDeliveryOption,
        fee: selectedDeliveryOption.fee
      } : undefined,
      totalAmount: totalAmount,
      subtotal: subtotal,
      deliveryFee: deliveryFee,
      discount: discount,
      tax: tax,
      total: totalAmount
    };

    // Add to bookings
    const bookingId = await addBooking(newBooking);
    const bookingWithId: Booking = { ...newBooking, id: bookingId };
    setBookings(prev => [...prev, bookingWithId]);

    // Create client activity
    const newActivity: Omit<ClientActivity, 'id'> = {
      clientId: clientId,
      date: new Date().toISOString(),
      type: 'booking',
      description: `Booked ${selectedProducts.map(p => p.name).join(', ')}`,
      amount: totalAmount,
      bookingId: bookingId,
      quoteNumber: quoteNumber
    };

    const activityId = await addClientActivity(newActivity);
    const activityWithId: ClientActivity = { ...newActivity, id: activityId };
    setClientActivities(prev => [...prev, activityWithId]);

    // Reset the form to prevent duplicate submissions
    // We don't reset completely because we want to keep the confirmed booking on screen
    // Instead, we'll ensure the booking can't be submitted again
    setSelectedProducts([...selectedProducts]); // Create a new array reference
  };

  const resetForm = () => {
    setSelectedProducts([]);
    setContactInfo({
      name: '',
      email: '',
      phone: '',
      address: ''
    });
    setRentalPeriod({
      startDate: '',
      endDate: '',
      days: 1,
      rentalType: 'daily'
    });
    setCurrentQuoteNumber('');
    setCouponCode('');
    setAppliedCoupon(null);
    setQuoteGenerated(false);
    setSelectedDeliveryOption(null);
    window.scrollTo(0, 0);
    setStep(1);
  };

  const handleLogin = (user: User) => {
    setCurrentUser(user);
    setIsAuthenticated(true);

    // Store user in localStorage for persistent login
    localStorage.setItem('currentUser', JSON.stringify(user));

    // Admin users should see the booking page by default, not the admin dashboard
    // They can navigate to the admin dashboard using the admin button
    setIsAdmin(false);
  };

  const handleLogout = async () => {
    try {
      // Update local state
      setCurrentUser(null);
      setIsAuthenticated(false);
      setIsAdmin(false);

      // Remove user from localStorage on logout
      localStorage.removeItem('currentUser');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleAdminLogin = (success: boolean) => {
    if (currentUser && currentUser.role === 'admin') {
      setIsAdmin(success);
      setShowAdminLogin(false);
    } else {
      setShowAdminLogin(false);
      alert('You do not have permission to access the admin panel.');
    }
  };

  const handleProductUpdate = async (updatedProducts: Product[]) => {
    setProducts(updatedProducts);
    // Only add new products that don't have an ID
    for (const product of updatedProducts) {
      if (!product.id) {
        await addProduct(product);
      }
    }
  };

  const handleBookingUpdate = async (updatedBookings: Booking[]) => {
    setBookings(updatedBookings);
    for (const booking of updatedBookings) {
      if (booking.id) {
        // Update existing booking
        await updateBooking(booking);
      } else {
        // Add new booking
        await addBooking(booking);
      }
    }
  };

  const handleCouponUpdate = async (updatedCoupons: Coupon[]) => {
    setCoupons(updatedCoupons);
    for (const coupon of updatedCoupons) {
      if (coupon.id) {
        // Update existing coupon
        await updateCoupon(coupon);
      } else {
        // Add new coupon
        await addCoupon(coupon);
      }
    }
  };

  const handleCategoryUpdate = async (updatedCategories: string[]) => {
    console.log('Categories updated:', updatedCategories);
    setCategories(updatedCategories);

    // Ensure we fetch the latest categories from API
    try {
      const fetchedCategories = await getCategories();
      // Only update state if there's a difference to avoid loops
      if (JSON.stringify(fetchedCategories) !== JSON.stringify(updatedCategories)) {
        setCategories(fetchedCategories);
      }
    } catch (error) {
      console.error('Error refreshing categories after update:', error);
    }
  };

  const handleSystemSettingsUpdate = async (updatedSettings: SystemSettings) => {
    setSystemSettings(updatedSettings);
    await updateSystemSettings(updatedSettings);
  };

  const handleCouponApply = (code: string, customCoupon?: Coupon) => {
    // If a custom coupon is provided (from the discount modal), use it directly
    if (customCoupon) {
      setAppliedCoupon(customCoupon);
      return true;
    }

    // Otherwise, search for a coupon with the provided code
    if (code) {
      const coupon = coupons.find(c =>
        c.code.toLowerCase() === code.toLowerCase() &&
        c.active &&
        new Date(c.expiryDate) > new Date()
      );

      if (coupon) {
        setAppliedCoupon(coupon);
        return true;
      }
    } else if (code === '') {
      // Empty code means remove the coupon
      setAppliedCoupon(null);
      return true;
    }

    setAppliedCoupon(null);
    return false;
  };

  const handleUpdateSelectedProducts = (updatedProducts: Product[]) => {
    setSelectedProducts(updatedProducts);
  };

  const handleCreateQuote = () => {
    setShowCreateQuoteModal(true);
    setAdminQuoteProducts([]);
  };

  const handleAdminProductSelect = (product: Product) => {
    const existingProduct = adminQuoteProducts.find(p => p.id === product.id);

    if (existingProduct) {
      // Update quantity
      setAdminQuoteProducts(
        adminQuoteProducts.map(p =>
          p.id === product.id ? { ...p, quantity: p.quantity + 1 } : p
        )
      );
    } else {
      // Add new product
      setAdminQuoteProducts([...adminQuoteProducts, { ...product, quantity: 1 }]);
    }
  };

  const handleAdminProductRemove = (productId: string) => {
    setAdminQuoteProducts(adminQuoteProducts.filter(p => p.id !== productId));
  };

  const handleAdminQuoteCreate = () => {
    setSelectedProducts(adminQuoteProducts);
    setShowCreateQuoteModal(false);
    setIsAdmin(false);
    resetForm();
    setStep(2);
  };

  const handleUsersUpdate = (updatedUsers: User[]) => {
    setUsers(updatedUsers);
  };

  // Handle vendor update
  const handleVendorUpdate = async (updatedVendors: Vendor[]) => {
    setVendors(updatedVendors);
  };

  // Show login screen if not authenticated
  if (!isLoading && !error && !isAuthenticated) {
    return <Login onLogin={handleLogin} />;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Add a test render before the main return
  console.log('Rendering main app with products:', products.length);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-blue-600 text-white p-3 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Logo width={48} height={24} color="#ffffff" />
            <h1 className="text-xl md:text-2xl font-bold hidden md:block">TW- Booking Management</h1>
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden p-2 rounded-md hover:bg-blue-700"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            <Menu size={24} />
          </button>

          {/* Desktop navigation */}
          <div className="hidden md:flex space-x-2 md:space-x-4 items-center">
            {currentUser && (
              <div className="text-sm mr-4">
                <span className="opacity-75">Logged in as: </span>
                <span className="font-semibold">{currentUser.name}</span>
                <span className="ml-2 px-2 py-0.5 text-xs bg-blue-700 rounded-full">
                  {currentUser.role}
                </span>
              </div>
            )}

            {!isAdmin && currentUser && (
              <>
                <button
                  onClick={() => setShowAddTempProduct(true)}
                  className="bg-white text-blue-600 px-2 py-1 md:px-4 md:py-2 rounded-md font-medium hover:bg-blue-50 transition-colors text-sm md:text-base flex items-center"
                >
                  <Plus size={18} className="mr-2" />
                  Quick Add
                </button>
                <button
                  onClick={resetForm}
                  className="bg-white text-blue-600 px-2 py-1 md:px-4 md:py-2 rounded-md font-medium hover:bg-blue-50 transition-colors text-sm md:text-base"
                >
                  New
                </button>
                {currentUser.role === 'admin' && (
                  <button
                    onClick={() => setIsAdmin(true)}
                    className="bg-blue-700 text-white p-2 rounded-md hover:bg-blue-800 transition-colors"
                    aria-label="Admin Panel"
                  >
                    <Shield size={18} />
                  </button>
                )}
              </>
            )}

            <button
              onClick={handleLogout}
              className="bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-800 transition-colors"
            >
              Logout
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden mt-2 pt-2 border-t border-blue-500">
            {currentUser && (
              <div className="text-sm mb-3 font-medium">
                <span className="opacity-75">Logged in as: </span>
                <span className="font-semibold">{currentUser.name}</span>
                <span className="ml-2 px-2 py-0.5 text-xs bg-blue-700 rounded-full">
                  {currentUser.role}
                </span>
              </div>
            )}

            <div className="flex flex-col space-y-2">
              {!isAdmin && currentUser && (
                <>
                  <button
                    onClick={() => {
                      setShowAddTempProduct(true);
                      setMobileMenuOpen(false);
                    }}
                    className="bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors text-sm flex items-center"
                  >
                    <Plus size={18} className="mr-2" />
                    Quick Add
                  </button>
                  <button
                    onClick={() => {
                      resetForm();
                      setMobileMenuOpen(false);
                    }}
                    className="bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors text-sm"
                  >
                    New
                  </button>
                  {currentUser.role === 'admin' && (
                    <button
                      onClick={() => {
                        setIsAdmin(true);
                        setMobileMenuOpen(false);
                      }}
                      className="bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-800 transition-colors text-sm flex items-center"
                    >
                      <Shield size={18} className="mr-2" />
                      Admin Panel
                    </button>
                  )}
                </>
              )}

              <button
                onClick={() => {
                  handleLogout();
                  setMobileMenuOpen(false);
                }}
                className="bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-800 transition-colors text-sm"
              >
                Logout
              </button>
            </div>
          </div>
        )}
      </header>

      {/* Add Temporary Product Modal */}
      {showAddTempProduct && (
        <AddTemporaryProduct
          onClose={() => setShowAddTempProduct(false)}
          onAdd={(product) => {
            setSelectedProducts([product]);
            setShowAddTempProduct(false);
            setStep(2);
          }}
        />
      )}

      {/* Admin Login Modal */}
      {showAdminLogin && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <AdminLogin onLogin={handleAdminLogin} onCancel={() => setShowAdminLogin(false)} />
          </div>
        </div>
      )}

      {/* Create Quote Modal */}
      {showCreateQuoteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">Create New Quote</h2>
              <button
                onClick={() => setShowCreateQuoteModal(false)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>

            <div className="mb-4">
              <h3 className="font-medium mb-2">Select Products</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {products.map(product => (
                  <div
                    key={product.id}
                    className="border rounded-md p-3 flex items-center cursor-pointer hover:bg-gray-50"
                    onClick={() => handleAdminProductSelect(product)}
                  >
                    <div className="h-12 w-12 flex-shrink-0 rounded overflow-hidden mr-3">
                      <img src={product.image} alt={product.name} className="h-full w-full object-cover" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{product.name}</p>
                      <p className="text-sm text-gray-500">${product.dailyRate}/day</p>
                    </div>
                    <div className="ml-2">
                      <button className="p-1 bg-blue-100 rounded-full text-blue-600">
                        <Plus size={16} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {adminQuoteProducts.length > 0 && (
              <div className="mb-4">
                <h3 className="font-medium mb-2">Selected Products</h3>
                <div className="border rounded-md overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
                        <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase">Quantity</th>
                        <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {adminQuoteProducts.map(product => (
                        <tr key={product.id}>
                          <td className="px-4 py-2">
                            <div className="flex items-center">
                              <div className="h-8 w-8 flex-shrink-0 rounded overflow-hidden mr-2">
                                <img src={product.image} alt={product.name} className="h-full w-full object-cover" />
                              </div>
                              <span>{product.name}</span>
                            </div>
                          </td>
                          <td className="px-4 py-2 text-center">
                            <div className="flex items-center justify-center">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setAdminQuoteProducts(
                                    adminQuoteProducts.map(p =>
                                      p.id === product.id ? { ...p, quantity: Math.max(1, p.quantity - 1) } : p
                                    )
                                  );
                                }}
                                className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                              >
                                <Minus size={14} />
                              </button>
                              <span className="mx-2">{product.quantity}</span>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setAdminQuoteProducts(
                                    adminQuoteProducts.map(p =>
                                      p.id === product.id ? { ...p, quantity: Math.min(p.stock, p.quantity + 1) } : p
                                    )
                                  );
                                }}
                                className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                              >
                                <Plus size={14} />
                              </button>
                            </div>
                          </td>
                          <td className="px-4 py-2 text-right">
                            <button
                              onClick={() => handleAdminProductRemove(product.id)}
                              className="p-1 rounded-full text-red-600 hover:bg-red-100"
                            >
                              <Trash2 size={16} />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            <div className="flex justify-end mt-4">
              <button
                onClick={() => setShowCreateQuoteModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={handleAdminQuoteCreate}
                className="px-4 py-2 bg-blue-600 text-white rounded-md"
                disabled={adminQuoteProducts.length === 0}
              >
                Create Quote
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="container mx-auto py-4 md:py-6 px-4">
        {isAdmin ? (
          <AdminDashboard
            products={products}
            bookings={bookings}
            coupons={coupons}
            categories={categories}
            systemSettings={systemSettings}
            users={users}
            currentUser={currentUser!}
            onProductsUpdate={handleProductUpdate}
            onBookingsUpdate={handleBookingUpdate}
            onCouponsUpdate={handleCouponUpdate}
            onCategoriesUpdate={handleCategoryUpdate}
            onSystemSettingsUpdate={handleSystemSettingsUpdate}
            onUsersUpdate={handleUsersUpdate}
            onCreateQuote={handleCreateQuote}
            vendors={vendors}
            onVendorUpdate={handleVendorUpdate}
          />
        ) : (
          <>
            {/* Progress Steps */}
            <div className="flex justify-between mb-6 max-w-3xl mx-auto overflow-x-auto py-2">
              <div className={`flex flex-col items-center ${step >= 1 ? 'text-blue-600' : 'text-gray-400'} min-w-[80px]`}>
                <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center mb-1 md:mb-2 ${step >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  <ShoppingCart size={16} className="md:hidden" />
                  <ShoppingCart size={20} className="hidden md:block" />
                </div>
                <span className="text-xs md:text-sm font-medium text-center">Select Products</span>
              </div>
              <div className={`flex flex-col items-center ${step >= 2 ? 'text-blue-600' : 'text-gray-400'} min-w-[80px]`}>
                <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center mb-1 md:mb-2 ${step >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  <Calendar size={16} className="md:hidden" />
                  <Calendar size={20} className="hidden md:block" />
                </div>
                <span className="text-xs md:text-sm font-medium text-center">Rental Period</span>
              </div>
              <div className={`flex flex-col items-center ${step >= 3 ? 'text-blue-600' : 'text-gray-400'} min-w-[80px]`}>
                <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center mb-1 md:mb-2 ${step >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  <Truck size={16} className="md:hidden" />
                  <Truck size={20} className="hidden md:block" />
                </div>
                <span className="text-xs md:text-sm font-medium text-center">Delivery</span>
              </div>
              <div className={`flex flex-col items-center ${step >= 4 ? 'text-blue-600' : 'text-gray-400'} min-w-[80px]`}>
                <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center mb-1 md:mb-2 ${step >= 4 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  <UserIcon size={16} className="md:hidden" />
                  <UserIcon size={20} className="hidden md:block" />
                </div>
                <span className="text-xs md:text-sm font-medium text-center">Contact Info</span>
              </div>
              <div className={`flex flex-col items-center ${step >= 5 ? 'text-blue-600' : 'text-gray-400'} min-w-[80px]`}>
                <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center mb-1 md:mb-2 ${step >= 5 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  <ShoppingCart size={16} className="md:hidden" />
                  <ShoppingCart size={20} className="hidden md:block" />
                </div>
                <span className="text-xs md:text-sm font-medium text-center">Review</span>
              </div>
              <div className={`flex flex-col items-center ${step >= 6 ? 'text-blue-600' : 'text-gray-400'} min-w-[80px]`}>
                <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center mb-1 md:mb-2 ${step >= 6 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                  <FileText size={16} className="md:hidden" />
                  <FileText size={20} className="hidden md:block" />
                </div>
                <span className="text-xs md:text-sm font-medium text-center">Quote</span>
              </div>
            </div>

            {/* Content */}
            <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-4 md:p-6">
              {step === 1 && (
                <ProductList
                  products={products}
                  onProductSelect={handleProductSelect}
                />
              )}

              {step === 2 && (
                <RentalPeriod
                  rentalPeriod={rentalPeriod}
                  onRentalPeriodSubmit={handleRentalPeriodSubmit}
                  onBack={() => setStep(1)}
                />
              )}

              {step === 3 && (
                <DeliveryOptions
                  deliveryOptions={systemSettings.deliveryOptions}
                  onDeliverySelect={setSelectedDeliveryOption}
                  onBack={() => setStep(2)}
                  onSubmit={() => setStep(4)}
                />
              )}

              {step === 4 && (
                <ContactForm
                  contactInfo={contactInfo}
                  onContactSubmit={handleContactSubmit}
                  onBack={() => setStep(3)}
                />
              )}

              {step === 5 && (
                <ReviewBooking
                  selectedProducts={selectedProducts}
                  contactInfo={contactInfo}
                  rentalPeriod={rentalPeriod}
                  couponCode={couponCode}
                  appliedCoupon={appliedCoupon}
                  deliveryOption={selectedDeliveryOption}
                  systemSettings={systemSettings}
                  onCouponCodeChange={setCouponCode}
                  onCouponApply={handleCouponApply}
                  onProductsUpdate={handleUpdateSelectedProducts}
                  onSubmit={handleReviewSubmit}
                  onBack={() => setStep(4)}
                />
              )}

              {step === 6 && quoteGenerated && (
                <Quote
                  quoteNumber={currentQuoteNumber}
                  selectedProducts={selectedProducts}
                  contactInfo={contactInfo}
                  rentalPeriod={rentalPeriod}
                  appliedCoupon={appliedCoupon}
                  deliveryOption={selectedDeliveryOption}
                  systemSettings={systemSettings}
                  onNewReservation={resetForm}
                  onBack={() => setStep(5)}
                  onFinalize={handleQuoteFinalize}
                />
              )}
            </div>
          </>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-6 mt-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h3 className="text-xl font-bold flex items-center">
                <Truck size={20} className="mr-2" /> TW- Booking Management
              </h3>
              <p className="text-gray-400 mt-1">Quality equipment for all your needs</p>
            </div>
            <div className="flex flex-col space-y-2">
              <div className="flex items-center">
                <Phone size={16} className="mr-2" />
                <span>(*************</span>
              </div>
              <div className="flex items-center">
                <Mail size={16} className="mr-2" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center">
                <Clock size={16} className="mr-2" />
                <span>Mon-Fri: 8am-6pm, Sat: 9am-4pm</span>
              </div>
            </div>
          </div>
          <div className="mt-6 text-center text-gray-400 text-sm">
            &copy; {new Date().getFullYear()} TW- Booking Management. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;