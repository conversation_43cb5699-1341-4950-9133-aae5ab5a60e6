<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
            handlePost($database);
            break;
        case 'PUT':
            handlePut($database);
            break;
        case 'DELETE':
            handleDelete($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Categories API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    $query = "SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != '' ORDER BY category";
    $categories = $database->getMany($query);
    
    // Extract category names
    $categoryNames = array_map(function($row) {
        return $row['category'];
    }, $categories);
    
    sendResponse($categoryNames);
}

function handlePost($database) {
    $data = getRequestData();
    
    validateRequired($data, ['name']);
    
    $categoryName = sanitizeInput($data['name']);
    
    // Check if category already exists
    $checkQuery = "SELECT COUNT(*) as count FROM products WHERE category = ?";
    $result = $database->getOne($checkQuery, [$categoryName]);
    
    if ($result['count'] > 0) {
        sendError('Category already exists');
    }
    
    // Create a sample product with this category to establish it
    $id = $database->generateId();
    $query = "INSERT INTO categories (id, name, description) VALUES (?, ?, ?)";
    $database->executeQuery($query, [$id, $categoryName, $data['description'] ?? '']);
    
    sendSuccess('Category created successfully', ['id' => $id, 'name' => $categoryName]);
}

function handlePut($database) {
    $data = getRequestData();
    
    validateRequired($data, ['oldName', 'newName']);
    
    $oldName = sanitizeInput($data['oldName']);
    $newName = sanitizeInput($data['newName']);
    
    // Update all products with this category
    $query = "UPDATE products SET category = ? WHERE category = ?";
    $rowsAffected = $database->update($query, [$newName, $oldName]);
    
    if ($rowsAffected === 0) {
        sendError('Category not found', 404);
    }
    
    sendSuccess('Category updated successfully');
}

function handleDelete($database) {
    $categoryName = $_GET['name'] ?? null;
    
    if (!$categoryName) {
        sendError('Category name is required');
    }
    
    $categoryName = sanitizeInput($categoryName);
    
    // Update all products in this category to 'Uncategorized'
    $query = "UPDATE products SET category = 'Uncategorized' WHERE category = ?";
    $rowsAffected = $database->update($query, [$categoryName]);
    
    if ($rowsAffected === 0) {
        sendError('Category not found', 404);
    }
    
    sendSuccess('Category deleted successfully');
}
?>
