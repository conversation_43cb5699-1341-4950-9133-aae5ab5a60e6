<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
            handlePost($database);
            break;
        case 'PUT':
            handlePut($database);
            break;
        case 'DELETE':
            handleDelete($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Equipment API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    $query = "SELECT * FROM products ORDER BY name";
    $products = $database->getMany($query);
    
    // Format products to match frontend expectations
    $formattedProducts = array_map(function($product) {
        return [
            'id' => $product['id'],
            'name' => $product['name'],
            'category' => $product['category'],
            'sku' => $product['sku'],
            'barcode' => $product['barcode'],
            'dailyRate' => (float)$product['daily_rate'],
            'weeklyRate' => $product['weekly_rate'] ? (float)$product['weekly_rate'] : null,
            'image' => $product['image'],
            'description' => $product['description'],
            'available' => (bool)$product['available'],
            'quantity' => (int)$product['quantity'],
            'stock' => (int)$product['stock'],
            'featured' => (bool)$product['featured'],
            'customDays' => $product['custom_days'] ? (int)$product['custom_days'] : null,
            'customPrice' => $product['custom_price'] ? (float)$product['custom_price'] : null,
            'temporaryDailyRate' => $product['temporary_daily_rate'] ? (float)$product['temporary_daily_rate'] : null,
            'temporaryWeeklyRate' => $product['temporary_weekly_rate'] ? (float)$product['temporary_weekly_rate'] : null,
            'isExternalVendorItem' => (bool)$product['is_external_vendor_item'],
            'vendorId' => $product['vendor_id'],
            'vendorSku' => $product['vendor_sku'],
            'vendorCost' => $product['vendor_cost'] ? (float)$product['vendor_cost'] : null,
            'profitMargin' => $product['profit_margin'] ? (float)$product['profit_margin'] : null
        ];
    }, $products);
    
    sendResponse($formattedProducts);
}

function handlePost($database) {
    $data = getRequestData();
    
    validateRequired($data, ['name', 'category', 'sku', 'dailyRate']);
    
    $id = $database->generateId();
    
    $query = "INSERT INTO products (
        id, name, category, sku, barcode, daily_rate, weekly_rate, image, description,
        available, quantity, stock, featured, custom_days, custom_price,
        temporary_daily_rate, temporary_weekly_rate, is_external_vendor_item,
        vendor_id, vendor_sku, vendor_cost, profit_margin
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $id,
        sanitizeInput($data['name']),
        sanitizeInput($data['category']),
        sanitizeInput($data['sku']),
        sanitizeInput($data['barcode'] ?? null),
        $data['dailyRate'],
        $data['weeklyRate'] ?? null,
        sanitizeInput($data['image'] ?? null),
        sanitizeInput($data['description'] ?? null),
        $data['available'] ?? true,
        $data['quantity'] ?? 0,
        $data['stock'] ?? 0,
        $data['featured'] ?? false,
        $data['customDays'] ?? null,
        $data['customPrice'] ?? null,
        $data['temporaryDailyRate'] ?? null,
        $data['temporaryWeeklyRate'] ?? null,
        $data['isExternalVendorItem'] ?? false,
        sanitizeInput($data['vendorId'] ?? null),
        sanitizeInput($data['vendorSku'] ?? null),
        $data['vendorCost'] ?? null,
        $data['profitMargin'] ?? null
    ];
    
    $database->executeQuery($query, $params);
    
    sendResponse(['id' => $id]);
}

function handlePut($database) {
    $data = getRequestData();
    
    validateRequired($data, ['id', 'name', 'category', 'sku', 'dailyRate']);
    
    $query = "UPDATE products SET
        name = ?, category = ?, sku = ?, barcode = ?, daily_rate = ?, weekly_rate = ?,
        image = ?, description = ?, available = ?, quantity = ?, stock = ?, featured = ?,
        custom_days = ?, custom_price = ?, temporary_daily_rate = ?, temporary_weekly_rate = ?,
        is_external_vendor_item = ?, vendor_id = ?, vendor_sku = ?, vendor_cost = ?, profit_margin = ?
    WHERE id = ?";
    
    $params = [
        sanitizeInput($data['name']),
        sanitizeInput($data['category']),
        sanitizeInput($data['sku']),
        sanitizeInput($data['barcode'] ?? null),
        $data['dailyRate'],
        $data['weeklyRate'] ?? null,
        sanitizeInput($data['image'] ?? null),
        sanitizeInput($data['description'] ?? null),
        $data['available'] ?? true,
        $data['quantity'] ?? 0,
        $data['stock'] ?? 0,
        $data['featured'] ?? false,
        $data['customDays'] ?? null,
        $data['customPrice'] ?? null,
        $data['temporaryDailyRate'] ?? null,
        $data['temporaryWeeklyRate'] ?? null,
        $data['isExternalVendorItem'] ?? false,
        sanitizeInput($data['vendorId'] ?? null),
        sanitizeInput($data['vendorSku'] ?? null),
        $data['vendorCost'] ?? null,
        $data['profitMargin'] ?? null,
        sanitizeInput($data['id'])
    ];
    
    $rowsAffected = $database->update($query, $params);
    
    if ($rowsAffected === 0) {
        sendError('Product not found', 404);
    }
    
    sendSuccess('Product updated successfully');
}

function handleDelete($database) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        sendError('Product ID is required');
    }
    
    $query = "DELETE FROM products WHERE id = ?";
    $rowsAffected = $database->delete($query, [sanitizeInput($id)]);
    
    if ($rowsAffected === 0) {
        sendError('Product not found', 404);
    }
    
    sendSuccess('Product deleted successfully');
}
?>
