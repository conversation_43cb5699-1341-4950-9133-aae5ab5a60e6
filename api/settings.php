<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
        case 'PUT':
            handleUpdate($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Settings API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    $query = "SELECT * FROM system_settings WHERE id = ?";
    $settings = $database->getOne($query, ['general']);
    
    if (!$settings) {
        // Return default settings if none exist
        $defaultSettings = [
            'taxRate' => 0,
            'enableTax' => false,
            'deliveryOptions' => [],
            'lastQuoteNumber' => 0
        ];
        
        sendResponse($defaultSettings);
        return;
    }
    
    $settingsData = $settings['settings_data'] ? json_decode($settings['settings_data'], true) : [];
    
    $response = [
        'taxRate' => (float)$settings['tax_rate'],
        'enableTax' => (bool)$settings['enable_tax'],
        'lastQuoteNumber' => (int)$settings['last_quote_number'],
        'deliveryOptions' => $settingsData['deliveryOptions'] ?? []
    ];
    
    sendResponse($response);
}

function handleUpdate($database) {
    $data = getRequestData();
    
    $query = "INSERT INTO system_settings (id, tax_rate, enable_tax, last_quote_number, settings_data)
              VALUES (?, ?, ?, ?, ?)
              ON DUPLICATE KEY UPDATE
              tax_rate = VALUES(tax_rate),
              enable_tax = VALUES(enable_tax),
              last_quote_number = VALUES(last_quote_number),
              settings_data = VALUES(settings_data)";
    
    $params = [
        'general',
        $data['taxRate'] ?? 0,
        $data['enableTax'] ?? false,
        $data['lastQuoteNumber'] ?? 0,
        json_encode($data)
    ];
    
    $database->executeQuery($query, $params);
    
    sendSuccess('Settings updated successfully');
}
?>
